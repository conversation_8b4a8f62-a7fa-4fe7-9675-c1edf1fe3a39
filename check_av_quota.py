#!/usr/bin/env python3
"""
Quick Alpha Vantage Quota Checker
Simple script to check if API keys are working
"""

import requests
from helpers.config import config

def quick_check():
    """Quick check of Alpha Vantage API keys"""
    print("🔍 Quick Alpha Vantage API Check")
    print("-" * 30)
    
    try:
        api_keys = config.get_alphavantage_api_keys()
        if not api_keys:
            print("❌ No API keys found in .env file")
            return
        
        for i, key in enumerate(api_keys, 1):
            print(f"\n🔑 Key{i} ({key[:8]}...):")
            
            # Simple test request
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": "AAPL",
                "apikey": key
            }
            
            try:
                response = requests.get(url, params=params, timeout=10)
                data = response.json()
                
                if "Global Quote" in data:
                    print("   ✅ Working")
                elif "Note" in data or "Information" in data:
                    msg = data.get("Note", data.get("Information", "Rate limited"))
                    print(f"   ⚠️ Rate Limited: {msg}")
                else:
                    print(f"   ❌ Error: {list(data.keys())}")
                    
            except Exception as e:
                print(f"   ❌ Network Error: {str(e)}")
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    quick_check()
