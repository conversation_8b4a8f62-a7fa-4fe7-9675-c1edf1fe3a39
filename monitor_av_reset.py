#!/usr/bin/env python3
"""
Alpha Vantage API Reset Monitor
Monitors when Alpha Vantage API keys reset (midnight EST)
"""

import requests
import time
from datetime import datetime, timezone, timedelta
from helpers.config import config

def get_est_time():
    """Get current time in EST"""
    est = timezone(timedelta(hours=-5))  # EST is UTC-5
    return datetime.now(est)

def time_until_reset():
    """Calculate time until next API reset (midnight EST)"""
    now = get_est_time()
    
    # Next midnight EST
    next_reset = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    
    time_diff = next_reset - now
    hours = int(time_diff.total_seconds() // 3600)
    minutes = int((time_diff.total_seconds() % 3600) // 60)
    
    return hours, minutes, next_reset

def test_single_key(api_key, key_name):
    """Test if a single API key is working"""
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "GLOBAL_QUOTE",
        "symbol": "AAPL",
        "apikey": api_key
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if "Global Quote" in data:
            return True, "Working"
        elif "Information" in data or "Note" in data:
            msg = data.get("Information", data.get("Note", "Rate limited"))
            return False, msg
        else:
            return False, "Unknown error"
            
    except Exception as e:
        return False, f"Network error: {str(e)}"

def monitor_reset():
    """Monitor API keys until they reset"""
    print("🕐 Alpha Vantage API Reset Monitor")
    print("=" * 40)
    
    try:
        api_keys = config.get_alphavantage_api_keys()
        if not api_keys:
            print("❌ No API keys found!")
            return
            
        print(f"📊 Monitoring {len(api_keys)} API key(s)")
        
        # Show current status
        print(f"\n⏰ Current EST Time: {get_est_time().strftime('%Y-%m-%d %H:%M:%S')}")
        hours, minutes, reset_time = time_until_reset()
        print(f"🔄 Next Reset: {reset_time.strftime('%Y-%m-%d %H:%M:%S')} EST")
        print(f"⏳ Time Until Reset: {hours}h {minutes}m")
        
        print(f"\n{'='*40}")
        print("Current API Key Status:")
        
        all_working = True
        for i, key in enumerate(api_keys, 1):
            working, message = test_single_key(key, f"Key{i}")
            status = "✅ Working" if working else f"❌ {message}"
            print(f"🔑 Key{i} ({key[:8]}...): {status}")
            if not working:
                all_working = False
        
        if all_working:
            print("\n🎉 All API keys are working! No need to wait.")
            return
            
        print(f"\n⏳ Waiting for reset in {hours}h {minutes}m...")
        print("💡 You can:")
        print("   • Use crypto analysis with basic indicators (no Alpha Vantage)")
        print("   • Wait for the reset")
        print("   • Consider upgrading to Alpha Vantage premium")
        print("\n🔄 Will check every hour until reset...")
        
        # Check every hour until reset
        while True:
            time.sleep(3600)  # Wait 1 hour
            
            current_time = get_est_time()
            hours, minutes, _ = time_until_reset()
            
            print(f"\n⏰ {current_time.strftime('%H:%M')} EST - Checking API status...")
            
            working_count = 0
            for i, key in enumerate(api_keys, 1):
                working, message = test_single_key(key, f"Key{i}")
                if working:
                    working_count += 1
                    print(f"✅ Key{i}: Working!")
                else:
                    print(f"❌ Key{i}: Still rate limited")
            
            if working_count > 0:
                print(f"\n🎉 {working_count}/{len(api_keys)} API key(s) are now working!")
                print("🚀 You can now use full Alpha Vantage crypto analysis!")
                break
            else:
                if hours == 0 and minutes < 30:
                    print(f"⏳ Reset expected in {minutes} minutes...")
                else:
                    print(f"⏳ Still waiting... {hours}h {minutes}m until reset")
                    
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")

def show_reset_info():
    """Show when API will reset without monitoring"""
    print("🕐 Alpha Vantage API Reset Information")
    print("=" * 40)
    
    current_time = get_est_time()
    hours, minutes, reset_time = time_until_reset()
    
    print(f"⏰ Current EST Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔄 Next Reset: {reset_time.strftime('%Y-%m-%d %H:%M:%S')} EST")
    print(f"⏳ Time Until Reset: {hours}h {minutes}m")
    
    print(f"\n💡 Alpha Vantage Free Tier:")
    print(f"   • 25 requests per day")
    print(f"   • Resets at midnight EST (UTC-5)")
    print(f"   • Premium plans remove limits")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_reset_info()
    else:
        monitor_reset()
