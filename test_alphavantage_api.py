#!/usr/bin/env python3
"""
Alpha Vantage API Key Testing Script
Tests API keys for rate limits, validity, and available quota
"""

import requests
import json
import time
from datetime import datetime
from helpers.config import config

def test_api_key(api_key, key_name="API Key"):
    """Test a single Alpha Vantage API key"""
    print(f"\n🔑 Testing {key_name}: {api_key[:8]}...")
    
    # Test with a simple quote request (minimal quota usage)
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "GLOBAL_QUOTE",
        "symbol": "AAPL",  # Use stock symbol to avoid crypto-specific issues
        "apikey": api_key
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        print(f"📊 Response keys: {list(data.keys())}")
        
        # Check for different response types
        if "Error Message" in data:
            print(f"❌ API Error: {data['Error Message']}")
            return False, "error", data["Error Message"]
            
        elif "Note" in data:
            print(f"⚠️ Rate Limited: {data['Note']}")
            return False, "rate_limited", data["Note"]
            
        elif "Information" in data:
            print(f"⚠️ Information: {data['Information']}")
            return False, "rate_limited", data["Information"]
            
        elif "Global Quote" in data:
            quote = data["Global Quote"]
            symbol = quote.get("01. symbol", "N/A")
            price = quote.get("05. price", "N/A")
            print(f"✅ API Working: {symbol} = ${price}")
            return True, "working", f"Successfully fetched {symbol} quote"
            
        else:
            print(f"❓ Unexpected response: {data}")
            return False, "unknown", "Unexpected response format"
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network Error: {str(e)}")
        return False, "network_error", str(e)
    except json.JSONDecodeError as e:
        print(f"❌ JSON Error: {str(e)}")
        return False, "json_error", str(e)
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")
        return False, "unexpected_error", str(e)

def test_crypto_indicator(api_key, key_name="API Key"):
    """Test crypto-specific indicator request"""
    print(f"\n📈 Testing crypto indicator with {key_name}...")
    
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "RSI",
        "symbol": "BTCUSD",
        "interval": "daily",
        "time_period": 14,
        "series_type": "close",
        "apikey": api_key
    }
    
    try:
        response = requests.get(url, params=params, timeout=15)
        data = response.json()
        
        if "Technical Analysis: RSI" in data:
            rsi_data = data["Technical Analysis: RSI"]
            latest_date = max(rsi_data.keys())
            rsi_value = rsi_data[latest_date]["RSI"]
            print(f"✅ Crypto RSI Working: BTC RSI = {rsi_value} on {latest_date}")
            return True, "working", f"RSI: {rsi_value}"
        else:
            print(f"❌ Crypto RSI Failed: {list(data.keys())}")
            if "Information" in data:
                print(f"   Rate limit info: {data['Information']}")
            return False, "failed", data.get("Information", "Unknown error")
            
    except Exception as e:
        print(f"❌ Crypto test error: {str(e)}")
        return False, "error", str(e)

def main():
    """Main testing function"""
    print("🚀 Alpha Vantage API Key Testing Script")
    print("=" * 50)
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get API keys from config
    try:
        api_keys = config.get_alphavantage_api_keys()
        if not api_keys:
            print("❌ No Alpha Vantage API keys found in .env file!")
            print("   Please add ALPHAVANTAGE_API_KEY and ALPHAVANTAGE_API_KEY_2 to your .env file")
            return
            
        print(f"🔍 Found {len(api_keys)} API key(s)")
        
    except Exception as e:
        print(f"❌ Error loading API keys: {str(e)}")
        return
    
    # Test each API key
    results = []
    
    for i, api_key in enumerate(api_keys, 1):
        key_name = f"Key{i}"
        print(f"\n{'='*20} {key_name} {'='*20}")
        
        # Basic API test
        working, status, message = test_api_key(api_key, key_name)
        
        result = {
            "key_name": key_name,
            "key_preview": api_key[:8] + "...",
            "basic_test": {
                "working": working,
                "status": status,
                "message": message
            }
        }
        
        # If basic test works, try crypto indicator
        if working:
            time.sleep(1)  # Small delay between requests
            crypto_working, crypto_status, crypto_message = test_crypto_indicator(api_key, key_name)
            result["crypto_test"] = {
                "working": crypto_working,
                "status": crypto_status,
                "message": crypto_message
            }
        else:
            result["crypto_test"] = {
                "working": False,
                "status": "skipped",
                "message": "Basic test failed"
            }
        
        results.append(result)
        
        # Delay between API key tests
        if i < len(api_keys):
            time.sleep(2)
    
    # Summary
    print(f"\n{'='*50}")
    print("📋 SUMMARY")
    print(f"{'='*50}")
    
    working_keys = 0
    for result in results:
        key_name = result["key_name"]
        basic_ok = result["basic_test"]["working"]
        crypto_ok = result["crypto_test"]["working"]
        
        if basic_ok and crypto_ok:
            status_emoji = "✅"
            status_text = "WORKING"
            working_keys += 1
        elif basic_ok:
            status_emoji = "⚠️"
            status_text = "BASIC ONLY"
        else:
            status_emoji = "❌"
            status_text = "FAILED"
        
        print(f"{status_emoji} {key_name} ({result['key_preview']}): {status_text}")
        
        if not basic_ok:
            print(f"   Issue: {result['basic_test']['message']}")
        elif not crypto_ok:
            print(f"   Crypto Issue: {result['crypto_test']['message']}")
    
    print(f"\n🎯 Result: {working_keys}/{len(api_keys)} API keys fully working")
    
    if working_keys == 0:
        print("\n💡 Recommendations:")
        print("   • Wait for daily rate limits to reset (resets at midnight EST)")
        print("   • Consider upgrading to Alpha Vantage premium plan")
        print("   • Use crypto analysis with basic indicators only")
        print("   • Try again tomorrow")
    elif working_keys < len(api_keys):
        print("\n💡 Some keys are rate limited. The working keys will be used automatically.")
    else:
        print("\n🎉 All API keys are working! You're ready for professional crypto analysis.")

if __name__ == "__main__":
    main()
